<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design To Text Plugin</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #ffffff;
            color: #333333;
            font-size: 12px;
            line-height: 1.4;
            padding: 16px;
        }
        
        .header {
            margin-bottom: 16px;
        }
        
        .title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .description {
            color: #666666;
            margin-bottom: 16px;
            font-size: 12px;
        }
        
        .options {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            border: 1px solid #e9ecef;
        }
        
        .option-group {
            margin-bottom: 12px;
        }
        
        .option-group:last-child {
            margin-bottom: 0;
        }
        
        .option-label {
            font-weight: 500;
            margin-bottom: 6px;
            color: #495057;
            font-size: 11px;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin: 0;
        }
        
        .checkbox-item label {
            font-size: 11px;
            color: #666;
            cursor: pointer;
        }
        
        .format-selector {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .format-selector select {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 11px;
            background-color: white;
        }
        
        .loading {
            text-align: center;
            color: #666666;
            font-size: 12px;
            margin-bottom: 16px;
            display: none;
        }
        
        .output {
            background-color: #f4f4f4;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 16px;
            display: none;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.4;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .button {
            width: 100%;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-bottom: 8px;
            font-family: inherit;
            transition: background-color 0.2s;
        }
        
        .button-primary {
            background-color: #18a0fb;
            color: white;
        }
        
        .button-primary:hover:not(:disabled) {
            background-color: #0d8ce0;
        }
        
        .button-secondary {
            background-color: #f0f0f0;
            color: #333333;
            border: 1px solid #ccc;
        }
        
        .button-secondary:hover:not(:disabled) {
            background-color: #e0e0e0;
        }
        
        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .stats {
            font-size: 11px;
            color: #666;
            margin-top: 8px;
            text-align: center;
        }
        
        .copy-status {
            font-size: 11px;
            color: #28a745;
            text-align: center;
            margin-top: 4px;
            display: none;
        }
        
        .copy-error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Design To Text Plugin</div>
        <div class="description">Select a layer and click "Generate" to see its properties.</div>
    </div>
    
    <div class="options">
        <div class="option-group">
            <div class="option-label">Output Format:</div>
            <div class="format-selector">
                <select id="formatSelect">
                    <option value="text">Text Format</option>
                    <option value="json">JSON Format</option>
                </select>
            </div>
        </div>
        
        <div class="option-group">
            <div class="option-label">Include Properties:</div>
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="basicProps" checked>
                    <label for="basicProps">Basic</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="positionProps" checked>
                    <label for="positionProps">Position</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="textProps" checked>
                    <label for="textProps">Text</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="appearanceProps" checked>
                    <label for="appearanceProps">Appearance</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="layoutProps" checked>
                    <label for="layoutProps">Layout</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="effectsProps" checked>
                    <label for="effectsProps">Effects</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="exportProps" checked>
                    <label for="exportProps">Export</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="advancedProps" checked>
                    <label for="advancedProps">Advanced (Vector, Boolean, Mask)</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="componentsProps" checked>
                    <label for="componentsProps">Components</label>
                </div>
            </div>
        </div>
    </div>

    <div class="options">
        <div class="option-group">
            <div class="option-label">Code Generation:</div>
            <div class="format-selector">
                <select id="codeFormatSelect">
                    <option value="css">CSS</option>
                    <option value="ios">iOS (Swift)</option>
                    <option value="android">Android (Kotlin)</option>
                    <option value="react">React (JSX)</option>
                </select>
            </div>
        </div>
        <button id="generateCodeBtn" class="button button-secondary">Generate Code</button>
    </div>

    <div class="options">
        <div class="option-group">
            <div class="option-label">Design System Analysis:</div>
        </div>
        <button id="generateSpecsBtn" class="button button-secondary">Analyze Design System</button>
    </div>
    
    <div id="loading" class="loading">
        Generating description...
    </div>
    
    <div id="output" class="output"></div>
    
    <button id="generateBtn" class="button button-primary">
        Generate Description
    </button>
    
    <button id="copyBtn" class="button button-secondary" disabled>
        Copy to Clipboard
    </button>
    
    <div id="copyStatus" class="copy-status"></div>
    
    <div id="stats" class="stats"></div>

    <script>
        let currentDescription = null;

        // Handle generate button click
        document.getElementById('generateBtn').addEventListener('click', function() {
            const loadingEl = document.getElementById('loading');
            const outputEl = document.getElementById('output');
            const generateBtn = document.getElementById('generateBtn');
            const copyBtn = document.getElementById('copyBtn');
            const statsEl = document.getElementById('stats');
            const copyStatusEl = document.getElementById('copyStatus');
            
            // Show loading state
            loadingEl.style.display = 'block';
            outputEl.style.display = 'none';
            generateBtn.disabled = true;
            generateBtn.textContent = 'Generating...';
            copyBtn.disabled = true;
            statsEl.textContent = '';
            copyStatusEl.style.display = 'none';
            
            // Get selected properties and format
            const selectedProps = {
                basic: document.getElementById('basicProps').checked,
                position: document.getElementById('positionProps').checked,
                text: document.getElementById('textProps').checked,
                appearance: document.getElementById('appearanceProps').checked,
                layout: document.getElementById('layoutProps').checked,
                effects: document.getElementById('effectsProps').checked,
                export: document.getElementById('exportProps').checked,
                advanced: document.getElementById('advancedProps').checked,
                components: document.getElementById('componentsProps').checked
            };
            
            const format = document.getElementById('formatSelect').value;
            
            // Send message to plugin
            parent.postMessage({ 
                pluginMessage: { 
                    type: 'GENERATE_DESCRIPTION',
                    payload: {
                        filters: selectedProps,
                        format: format
                    }
                } 
            }, '*');
        });

        // Handle copy button click with real clipboard functionality
        document.getElementById('copyBtn').addEventListener('click', async function() {
            if (currentDescription) {
                const copyBtn = document.getElementById('copyBtn');
                const copyStatusEl = document.getElementById('copyStatus');
                
                try {
                    // Try to use modern clipboard API
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        await navigator.clipboard.writeText(currentDescription);
                        copyStatusEl.textContent = '✓ Copied to clipboard!';
                        copyStatusEl.className = 'copy-status';
                    } else {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = currentDescription;
                        textArea.style.position = 'fixed';
                        textArea.style.left = '-999999px';
                        textArea.style.top = '-999999px';
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        copyStatusEl.textContent = '✓ Copied to clipboard!';
                        copyStatusEl.className = 'copy-status';
                    }
                    
                    copyStatusEl.style.display = 'block';
                    
                    // Hide status after 3 seconds
                    setTimeout(() => {
                        copyStatusEl.style.display = 'none';
                    }, 3000);
                    
                } catch (error) {
                    console.error('Failed to copy to clipboard:', error);
                    copyStatusEl.textContent = '✗ Failed to copy to clipboard';
                    copyStatusEl.className = 'copy-status copy-error';
                    copyStatusEl.style.display = 'block';
                    
                    // Hide error after 3 seconds
                    setTimeout(() => {
                        copyStatusEl.style.display = 'none';
                    }, 3000);
                }
            }
        });

        // Code generation button
        document.getElementById('generateCodeBtn').addEventListener('click', function() {
            const codeFormat = document.getElementById('codeFormatSelect').value;

            parent.postMessage({
                pluginMessage: {
                    type: 'GENERATE_CODE',
                    payload: {
                        platform: codeFormat
                    }
                }
            }, '*');
        });

        // Design specs button
        document.getElementById('generateSpecsBtn').addEventListener('click', function() {
            parent.postMessage({
                pluginMessage: {
                    type: 'GENERATE_DESIGN_SPECS',
                    payload: {}
                }
            }, '*');
        });

        // Listen for messages from the plugin
        window.onmessage = function(event) {
            const { type, payload } = event.data.pluginMessage;
            
            if (type === 'DESCRIPTION_GENERATED') {
                const loadingEl = document.getElementById('loading');
                const outputEl = document.getElementById('output');
                const generateBtn = document.getElementById('generateBtn');
                const copyBtn = document.getElementById('copyBtn');
                const statsEl = document.getElementById('stats');
                const copyStatusEl = document.getElementById('copyStatus');
                
                // Hide loading state
                loadingEl.style.display = 'none';
                generateBtn.disabled = false;
                generateBtn.textContent = 'Generate Description';
                
                // Show output
                currentDescription = payload;
                outputEl.textContent = payload;
                outputEl.style.display = 'block';
                copyBtn.disabled = false;
                copyStatusEl.style.display = 'none';
                
                // Show stats
                if (payload && payload !== 'Please select at least one element.' && !payload.startsWith('Error:')) {
                    const format = document.getElementById('formatSelect').value;
                    if (format === 'text') {
                        const lines = payload.split('\n').length;
                        const sections = (payload.match(/===/g) || []).length / 2;
                        statsEl.textContent = `Generated ${lines} lines across ${sections} sections`;
                    } else {
                        try {
                            const jsonObj = JSON.parse(payload);
                            const elementCount = jsonObj.elements ? jsonObj.elements.length : 1;
                            statsEl.textContent = `Generated JSON with ${elementCount} element${elementCount > 1 ? 's' : ''}`;
                        } catch (e) {
                            statsEl.textContent = 'Generated JSON output';
                        }
                    }
                }
            } else if (type === 'CODE_GENERATED') {
                const outputEl = document.getElementById('output');
                const copyBtn = document.getElementById('copyBtn');
                const statsEl = document.getElementById('stats');

                outputEl.textContent = payload;
                outputEl.style.display = 'block';
                copyBtn.disabled = false;

                if (payload && !payload.startsWith('Error:') && !payload.includes('Please select')) {
                    const lines = payload.split('\n').length;
                    statsEl.textContent = `Generated ${lines} lines of code`;
                }
            } else if (type === 'DESIGN_SPECS_GENERATED') {
                const outputEl = document.getElementById('output');
                const copyBtn = document.getElementById('copyBtn');
                const statsEl = document.getElementById('stats');

                outputEl.textContent = payload;
                outputEl.style.display = 'block';
                copyBtn.disabled = false;

                if (payload && !payload.startsWith('Error:') && !payload.includes('Please select')) {
                    try {
                        const specs = JSON.parse(payload);
                        const colorCount = specs.colorTokens ? specs.colorTokens.length : 0;
                        const typeCount = specs.typographyTokens ? specs.typographyTokens.length : 0;
                        const componentCount = specs.components ? specs.components.length : 0;
                        statsEl.textContent = `Found ${colorCount} colors, ${typeCount} typography styles, ${componentCount} components`;
                    } catch (e) {
                        statsEl.textContent = 'Generated design system analysis';
                    }
                }
            }
        };
    </script>
</body>
</html>
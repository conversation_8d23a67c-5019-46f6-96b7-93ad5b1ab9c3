/**
 * Helper function to get style information by ID
 * @param styleId The style ID to look up
 * @returns Promise containing style information or null if not found
 */
async function getStyleInfo(styleId: string): Promise<{name: string, type: string, properties?: any} | null> {
  try {
    const style = await figma.getStyleByIdAsync(styleId);
    if (!style) return null;

    const info: {name: string, type: string, properties?: any} = {
      name: style.name,
      type: style.type
    };

    // Add specific properties based on style type
    if (style.type === 'PAINT') {
      const paintStyle = style as PaintStyle;
      info.properties = {
        paints: paintStyle.paints
      };
    } else if (style.type === 'TEXT') {
      const textStyle = style as TextStyle;
      info.properties = {
        fontName: textStyle.fontName,
        fontSize: textStyle.fontSize,
        letterSpacing: textStyle.letterSpacing,
        lineHeight: textStyle.lineHeight,
        textCase: textStyle.textCase,
        textDecoration: textStyle.textDecoration
      };
    } else if (style.type === 'EFFECT') {
      const effectStyle = style as EffectStyle;
      info.properties = {
        effects: effectStyle.effects
      };
    }

    return info;
  } catch (error) {
    console.warn('Failed to get style info for ID:', styleId, error);
    return null;
  }
}

/**
 * Helper function to format gradient transform information
 * @param gradientTransform The gradient transform matrix
 * @returns Formatted transform description
 */
function formatGradientTransform(gradientTransform: Transform): string {
  const [[a, b, tx], [c, d, ty]] = gradientTransform;

  // Calculate angle for linear gradients
  const angle = Math.atan2(b, a) * (180 / Math.PI);

  // Calculate scale
  const scaleX = Math.sqrt(a * a + b * b);
  const scaleY = Math.sqrt(c * c + d * d);

  const transformInfo = [
    `Angle: ${angle.toFixed(1)}°`,
    `Scale: ${scaleX.toFixed(2)} × ${scaleY.toFixed(2)}`,
    `Translation: (${tx.toFixed(1)}, ${ty.toFixed(1)})`
  ];

  return transformInfo.join(', ');
}

/**
 * Generates a detailed textual description for a given Figma SceneNode.
 * This function extracts a comprehensive set of design properties and formats them
 * into a human-readable string, organized by category, exactly as requested.
 * @param node The Figma SceneNode to describe.
 * @param filters Object containing which property categories to include.
 * @param format The output format ('text' or 'json').
 * @returns A string containing the detailed description of the node.
 */
async function generateDescriptionForNode(node: SceneNode, filters: any, format: string = 'text'): Promise<string> {
  if (format === 'json') {
    return await generateJSONDescription(node, filters);
  }

  const props: string[] = [];

  // Basic Properties
  if (filters.basic) {
    props.push('=== BASIC PROPERTIES ===');
    props.push('Type: ' + node.type);
    props.push('Name: ' + node.name);
    if ('visible' in node && !node.visible) props.push('Visibility: Hidden');
    if ('locked' in node && node.locked) props.push('Editing: Locked');
    if ('opacity' in node && node.opacity !== 1) props.push('Opacity: ' + Math.round(node.opacity * 100) + '%');
    
    // Blend Mode
    if ('blendMode' in node && node.blendMode !== 'NORMAL') {
      props.push('Blend Mode: ' + String((node as any).blendMode));
    }
  }

  // Component Properties
  if (node.type === 'INSTANCE' && filters.basic) {
    props.push('\n=== COMPONENT PROPERTIES ===');
    props.push('Component Name: ' + (node.mainComponent ? node.mainComponent.name : 'Unknown'));
    props.push('Component Key: ' + (node.mainComponent ? node.mainComponent.key : 'Unknown'));
    if (node.componentProperties) {
      Object.keys(node.componentProperties).forEach(function(propName) {
        const prop = node.componentProperties[propName];
        props.push('Property: ' + propName + ' = ' + prop.value);
      });
    }
  }

  // Position and Size
  if (filters.position) {
    props.push('\n=== POSITION & SIZE ===');
    props.push('X Position: ' + Math.round(node.x) + 'px');
    props.push('Y Position: ' + Math.round(node.y) + 'px');
    props.push('Width: ' + Math.round(node.width) + 'px');
    props.push('Height: ' + Math.round(node.height) + 'px');
    if ('rotation' in node && node.rotation !== 0) props.push('Rotation: ' + Math.round(node.rotation) + '°');
    
    // Constraints
    if ('constraints' in node) {
      const constraints = node.constraints;
      if (constraints.horizontal !== 'MIN' || constraints.vertical !== 'MIN') {
        props.push('Constraints:');
        props.push('  Horizontal: ' + constraints.horizontal);
        props.push('  Vertical: ' + constraints.vertical);
      }
    }
  }

  // Text Node Properties
  if (node.type === 'TEXT' && filters.text) {
    props.push('\n=== TEXT PROPERTIES ===');
    if ('characters' in node) props.push('Content: "' + node.characters + '"');
    if ('fontName' in node && node.fontName !== figma.mixed) {
      props.push('Font Family: ' + node.fontName.family);
      props.push('Font Style: ' + node.fontName.style);
    }
    if ('fontSize' in node && node.fontSize !== figma.mixed) props.push('Font Size: ' + node.fontSize + 'px');
    if ('letterSpacing' in node && node.letterSpacing !== figma.mixed) {
        if (node.letterSpacing.unit === 'PIXELS') {
            props.push('Letter Spacing: ' + node.letterSpacing.value.toFixed(0) + 'px');
        } else {
            props.push('Letter Spacing: ' + node.letterSpacing.value.toFixed(2) + '%');
        }
    }
    if ('lineHeight' in node && node.lineHeight !== figma.mixed) {
      if (node.lineHeight.unit === 'AUTO') {
        props.push('Line Height: Auto');
      } else if (node.lineHeight.unit === 'PIXELS'){
        props.push('Line Height: ' + node.lineHeight.value.toFixed(0) + 'px');
      } else {
        props.push('Line Height: ' + node.lineHeight.value.toFixed(2) + '%');
      }
    }
    if ('textAlignHorizontal' in node) props.push('Text Alignment: ' + node.textAlignHorizontal);
    if ('textCase' in node && node.textCase !== 'ORIGINAL') {
        const textCase = node.textCase as any;
        props.push('Text Case: ' + textCase);
    }
    if ('textDecoration' in node && node.textDecoration !== 'NONE') {
        const textDecoration = node.textDecoration as any;
        props.push('Text Decoration: ' + textDecoration);
    } else {
        props.push('Text Decoration: None');
    }
    
    // Enhanced Text Style Reference
    if ('textStyleId' in node && node.textStyleId !== '') {
      const textStyleInfo = await getStyleInfo(String((node as any).textStyleId));
      if (textStyleInfo) {
        props.push('Text Style: "' + textStyleInfo.name + '" (' + textStyleInfo.type + ')');
        if (textStyleInfo.properties) {
          const textProps = textStyleInfo.properties;
          if (textProps.fontName && textProps.fontName !== figma.mixed) {
            props.push('  Style Font: ' + textProps.fontName.family + ' ' + textProps.fontName.style);
          }
          if (textProps.fontSize && textProps.fontSize !== figma.mixed) {
            props.push('  Style Font Size: ' + textProps.fontSize + 'px');
          }
        }
      } else {
        props.push('Text Style ID: ' + String((node as any).textStyleId));
      }
    }
    
    // Advanced Text Properties
    if ('paragraphIndent' in node && node.paragraphIndent !== 0) {
      props.push('Paragraph Indent: ' + node.paragraphIndent + 'px');
    }
    if ('paragraphSpacing' in node && node.paragraphSpacing !== 0) {
      props.push('Paragraph Spacing: ' + node.paragraphSpacing + 'px');
    }
    if ('textAutoResize' in node && node.textAutoResize !== 'NONE') {
      props.push('Auto Resize: ' + node.textAutoResize);
    }
  }

  // Appearance Properties
  if (filters.appearance) {
    props.push('\n=== APPEARANCE ===');

    // Enhanced Style References
    if ('fillStyleId' in node && node.fillStyleId !== '') {
      const fillStyleInfo = await getStyleInfo(String((node as any).fillStyleId));
      if (fillStyleInfo) {
        props.push('Fill Style: "' + fillStyleInfo.name + '" (' + fillStyleInfo.type + ')');
        if (fillStyleInfo.properties && fillStyleInfo.properties.paints) {
          props.push('  Style Paints: ' + fillStyleInfo.properties.paints.length + ' paint(s)');
        }
      } else {
        props.push('Fill Style ID: ' + String((node as any).fillStyleId));
      }
    }

    if ('strokeStyleId' in node && node.strokeStyleId !== '') {
      const strokeStyleInfo = await getStyleInfo(String((node as any).strokeStyleId));
      if (strokeStyleInfo) {
        props.push('Stroke Style: "' + strokeStyleInfo.name + '" (' + strokeStyleInfo.type + ')');
        if (strokeStyleInfo.properties && strokeStyleInfo.properties.paints) {
          props.push('  Style Paints: ' + strokeStyleInfo.properties.paints.length + ' paint(s)');
        }
      } else {
        props.push('Stroke Style ID: ' + String((node as any).strokeStyleId));
      }
    }

    if ('effectStyleId' in node && node.effectStyleId !== '') {
      const effectStyleInfo = await getStyleInfo(String((node as any).effectStyleId));
      if (effectStyleInfo) {
        props.push('Effect Style: "' + effectStyleInfo.name + '" (' + effectStyleInfo.type + ')');
        if (effectStyleInfo.properties && effectStyleInfo.properties.effects) {
          props.push('  Style Effects: ' + effectStyleInfo.properties.effects.length + ' effect(s)');
        }
      } else {
        props.push('Effect Style ID: ' + String((node as any).effectStyleId));
      }
    }
    
    // Enhanced Fill Properties
    if ('fills' in node && Array.isArray(node.fills) && node.fills.length > 0) {
        node.fills.forEach(function(fill: any, index: number) {
            if (fill.visible) {
              if (fill.type === 'SOLID') {
                const color = fill.color;
                const r = Math.round(color.r * 255);
                const g = Math.round(color.g * 255);
                const b = Math.round(color.b * 255);
                const a = fill.opacity ? fill.opacity.toFixed(2) : '1';
                props.push('Fill ' + (index + 1) + ' (Solid): rgba(' + r + ', ' + g + ', ' + b + ', ' + a + ')');
              } else if (fill.type === 'GRADIENT_LINEAR' || fill.type === 'GRADIENT_RADIAL' || fill.type === 'GRADIENT_ANGULAR' || fill.type === 'GRADIENT_DIAMOND') {
                props.push('Fill ' + (index + 1) + ' (' + fill.type + '): Gradient');

                // Enhanced gradient transform information
                if (fill.gradientTransform) {
                  props.push('  Transform: ' + formatGradientTransform(fill.gradientTransform));
                }

                if (fill.gradientStops) {
                  props.push('  Gradient Stops (' + fill.gradientStops.length + '):');
                  fill.gradientStops.forEach(function(stop: any, stopIndex: number) {
                    const r = Math.round(stop.color.r * 255);
                    const g = Math.round(stop.color.g * 255);
                    const b = Math.round(stop.color.b * 255);
                    props.push('    Stop ' + (stopIndex + 1) + ': rgba(' + r + ', ' + g + ', ' + b + ', ' + stop.color.a.toFixed(2) + ') at ' + Math.round(stop.position * 100) + '%');
                  });
                }
              } else if (fill.type === 'IMAGE') {
                props.push('Fill ' + (index + 1) + ' (Image): Image Fill');
                if (fill.imageHash) {
                  props.push('  Image Hash: ' + fill.imageHash);
                }
                if (fill.scaleMode) {
                  props.push('  Scale Mode: ' + fill.scaleMode);
                }
              } else {
                props.push('Fill ' + (index + 1) + ' (' + fill.type + '): ' + fill.type);
              }
            }
        });
    } else {
        props.push('Fill: None');
    }
    
    // Enhanced Stroke Properties
    if ('strokes' in node && Array.isArray(node.strokes) && node.strokes.length > 0) {
        node.strokes.forEach(function(stroke: any, index: number) {
            if (stroke.visible) {
              if (stroke.type === 'SOLID') {
                const color = stroke.color;
                const r = Math.round(color.r * 255);
                const g = Math.round(color.g * 255);
                const b = Math.round(color.b * 255);
                props.push('Stroke ' + (index + 1) + ' (Solid): rgb(' + r + ', ' + g + ', ' + b + ')');
              } else if (stroke.type === 'GRADIENT_LINEAR' || stroke.type === 'GRADIENT_RADIAL' || stroke.type === 'GRADIENT_ANGULAR' || stroke.type === 'GRADIENT_DIAMOND') {
                props.push('Stroke ' + (index + 1) + ' (' + stroke.type + '): Gradient Stroke');

                // Enhanced gradient transform information for strokes
                if (stroke.gradientTransform) {
                  props.push('  Transform: ' + formatGradientTransform(stroke.gradientTransform));
                }

                if (stroke.gradientStops) {
                  props.push('  Gradient Stops (' + stroke.gradientStops.length + '):');
                  stroke.gradientStops.forEach(function(stop: any, stopIndex: number) {
                    const r = Math.round(stop.color.r * 255);
                    const g = Math.round(stop.color.g * 255);
                    const b = Math.round(stop.color.b * 255);
                    props.push('    Stop ' + (stopIndex + 1) + ': rgba(' + r + ', ' + g + ', ' + b + ', ' + stop.color.a.toFixed(2) + ') at ' + Math.round(stop.position * 100) + '%');
                  });
                }
              } else {
                props.push('Stroke ' + (index + 1) + ' (' + stroke.type + '): ' + stroke.type);
              }
              
              if('strokeWeight' in node && node.strokeWeight !== figma.mixed) props.push('  Weight: ' + node.strokeWeight + 'px');
              if('strokeAlign' in node) props.push('  Alignment: ' + node.strokeAlign);
              if('strokeCap' in node && node.strokeCap !== 'NONE') props.push('  Cap: ' + String((node as any).strokeCap));
              if('strokeJoin' in node && node.strokeJoin !== 'MITER') props.push('  Join: ' + String((node as any).strokeJoin));
              if('dashPattern' in node && node.dashPattern && node.dashPattern.length > 0) {
                props.push('  Dash Pattern: ' + node.dashPattern.join(', ') + 'px');
              }
            }
        });
    } else {
        props.push('Stroke: None');
    }
    
    if ('cornerRadius' in node && typeof node.cornerRadius === 'number' && node.cornerRadius > 0) {
        props.push('Corner Radius: ' + node.cornerRadius + 'px');
    } else {
        props.push('Corner Radius: 0px');
    }
  }

  // Auto Layout Properties
  if ('layoutMode' in node && node.layoutMode !== 'NONE' && filters.layout) {
    props.push('\n=== AUTO LAYOUT ===');
    props.push('Direction: ' + node.layoutMode);
    props.push('Item Spacing: ' + node.itemSpacing + 'px');
    props.push('Padding Top: ' + node.paddingTop + 'px');
    props.push('Padding Right: ' + node.paddingRight + 'px');
    props.push('Padding Bottom: ' + node.paddingBottom + 'px');
    props.push('Padding Left: ' + node.paddingLeft + 'px');
    props.push('Primary Axis Alignment: ' + node.primaryAxisAlignItems);
    props.push('Counter Axis Alignment: ' + node.counterAxisAlignItems);
  }

  // Effects (Shadows, Blurs)
  if ('effects' in node && Array.isArray(node.effects) && node.effects.length > 0 && filters.effects) {
    props.push('\n=== EFFECTS ===');
    node.effects.forEach(function(effect: any, index: number) {
      if (effect.visible) {
        if (effect.type === 'DROP_SHADOW' || effect.type === 'INNER_SHADOW') {
          const color = effect.color;
          const r = Math.round(color.r * 255);
          const g = Math.round(color.g * 255);
          const b = Math.round(color.b * 255);
          const a = color.a.toFixed(2);
          props.push(effect.type + ' ' + (index + 1) + ':');
          props.push('  Color: rgba(' + r + ', ' + g + ', ' + b + ', ' + a + ')');
          props.push('  Offset X: ' + effect.offset.x + 'px');
          props.push('  Offset Y: ' + effect.offset.y + 'px');
          props.push('  Radius: ' + effect.radius + 'px');
          if (effect.spread) {
            props.push('  Spread: ' + effect.spread + 'px');
          }
        } else if (effect.type === 'LAYER_BLUR' || effect.type === 'BACKGROUND_BLUR') {
          props.push(effect.type + ': ' + effect.radius + 'px');
        }
      }
    });
  } else if (filters.effects) {
    props.push('\n=== EFFECTS ===');
    props.push('Effects: None');
  }

  // Export Properties
  if ('exportSettings' in node && node.exportSettings && node.exportSettings.length > 0 && filters.export) {
    props.push('\n=== EXPORT SETTINGS ===');
    node.exportSettings.forEach(function(exportSetting: any, index: number) {
      props.push('Export ' + (index + 1) + ':');
      props.push('  Format: ' + exportSetting.format);
      props.push('  Scale: ' + exportSetting.scale + 'x');
      if (exportSetting.suffix) {
        props.push('  Suffix: ' + exportSetting.suffix);
      }
    });
  }

  return props.join('\n');
}

/**
 * Generates a JSON description for a given Figma SceneNode.
 * @param node The Figma SceneNode to describe.
 * @param filters Object containing which property categories to include.
 * @returns A JSON string containing the structured description of the node.
 */
async function generateJSONDescription(node: SceneNode, filters: any): Promise<string> {
  const result: any = {
    name: node.name,
    type: node.type,
    id: node.id,
    properties: {}
  };

  // Basic Properties
  if (filters.basic) {
    result.properties.basic = {
      type: node.type,
      name: node.name,
      visible: 'visible' in node ? node.visible : true,
      locked: 'locked' in node ? node.locked : false,
      opacity: 'opacity' in node ? node.opacity : 1
    };

    // Blend Mode
    if ('blendMode' in node && node.blendMode !== 'NORMAL') {
      result.properties.basic.blendMode = String((node as any).blendMode);
    }

    // Component Properties
    if (node.type === 'INSTANCE') {
      result.properties.component = {
        componentName: node.mainComponent ? node.mainComponent.name : null,
        componentKey: node.mainComponent ? node.mainComponent.key : null,
        properties: node.componentProperties ? node.componentProperties : {}
      };
    }
  }

  // Position and Size
  if (filters.position) {
    result.properties.position = {
      x: Math.round(node.x),
      y: Math.round(node.y),
      width: Math.round(node.width),
      height: Math.round(node.height),
      rotation: 'rotation' in node ? Math.round(node.rotation) : 0
    };
    
    // Constraints
    if ('constraints' in node) {
      const constraints = node.constraints;
      if (constraints.horizontal !== 'MIN' || constraints.vertical !== 'MIN') {
        result.properties.position.constraints = {
          horizontal: constraints.horizontal,
          vertical: constraints.vertical
        };
      }
    }
  }

  // Text Properties
  if (node.type === 'TEXT' && filters.text) {
    result.properties.text = {
      content: 'characters' in node ? node.characters : '',
      fontFamily: 'fontName' in node && node.fontName !== figma.mixed ? node.fontName.family : null,
      fontSize: 'fontSize' in node && node.fontSize !== figma.mixed ? node.fontSize : null,
      textAlign: 'textAlignHorizontal' in node ? node.textAlignHorizontal : null,
      letterSpacing: 'letterSpacing' in node && node.letterSpacing !== figma.mixed ? node.letterSpacing : null,
      lineHeight: 'lineHeight' in node && node.lineHeight !== figma.mixed ? node.lineHeight : null,
      textStyleId: 'textStyleId' in node ? node.textStyleId : null
    };

    // Enhanced text style information
    if ('textStyleId' in node && node.textStyleId !== '') {
      const textStyleInfo = await getStyleInfo(String((node as any).textStyleId));
      if (textStyleInfo) {
        result.properties.text.textStyle = {
          name: textStyleInfo.name,
          type: textStyleInfo.type,
          properties: textStyleInfo.properties
        };
      }
    }
  }

  // Appearance Properties
  if (filters.appearance) {
    result.properties.appearance = {
      fills: 'fills' in node ? node.fills : [],
      strokes: 'strokes' in node ? node.strokes : [],
      cornerRadius: 'cornerRadius' in node ? node.cornerRadius : 0
    };

    // Enhanced Style References
    if ('fillStyleId' in node && node.fillStyleId !== '') {
      result.properties.appearance.fillStyleId = node.fillStyleId;
      const fillStyleInfo = await getStyleInfo(String((node as any).fillStyleId));
      if (fillStyleInfo) {
        result.properties.appearance.fillStyle = {
          name: fillStyleInfo.name,
          type: fillStyleInfo.type,
          properties: fillStyleInfo.properties
        };
      }
    }

    if ('strokeStyleId' in node && node.strokeStyleId !== '') {
      result.properties.appearance.strokeStyleId = node.strokeStyleId;
      const strokeStyleInfo = await getStyleInfo(String((node as any).strokeStyleId));
      if (strokeStyleInfo) {
        result.properties.appearance.strokeStyle = {
          name: strokeStyleInfo.name,
          type: strokeStyleInfo.type,
          properties: strokeStyleInfo.properties
        };
      }
    }

    if ('effectStyleId' in node && node.effectStyleId !== '') {
      result.properties.appearance.effectStyleId = node.effectStyleId;
      const effectStyleInfo = await getStyleInfo(String((node as any).effectStyleId));
      if (effectStyleInfo) {
        result.properties.appearance.effectStyle = {
          name: effectStyleInfo.name,
          type: effectStyleInfo.type,
          properties: effectStyleInfo.properties
        };
      }
    }
  }

  // Layout Properties
  if ('layoutMode' in node && node.layoutMode !== 'NONE' && filters.layout) {
    result.properties.layout = {
      mode: node.layoutMode,
      itemSpacing: node.itemSpacing,
      padding: {
        top: node.paddingTop,
        right: node.paddingRight,
        bottom: node.paddingBottom,
        left: node.paddingLeft
      },
      primaryAxisAlignment: node.primaryAxisAlignItems,
      counterAxisAlignment: node.counterAxisAlignItems
    };
  }

  // Effects
  if (filters.effects) {
    result.properties.effects = 'effects' in node ? node.effects : [];
  }

  // Export Settings
  if (filters.export) {
    result.properties.export = 'exportSettings' in node ? node.exportSettings : [];
  }

  return JSON.stringify(result, null, 2);
}

/**
 * Main entry point for the Figma plugin.
 * This function runs in the Figma plugin sandbox and handles interactions
 * between the Figma canvas and the plugin's UI.
 */
function main() {
  // Show the plugin UI with a specified width and height.
  figma.showUI(__html__, { width: 400, height: 600 });

  // Listen for messages sent from the plugin UI.
  figma.ui.onmessage = async function(msg: any) {
    const type = msg.type;
    const payload = msg.payload;

    if (type === 'GENERATE_DESCRIPTION') {
      try {
        const selection = figma.currentPage.selection;
        if (selection.length === 0) {
          figma.ui.postMessage({
            type: 'DESCRIPTION_GENERATED',
            payload: 'Please select at least one element.'
          });
          return;
        }

        // Extract filters and format from payload
        const filters = payload.filters || {
          basic: true,
          position: true,
          text: true,
          appearance: true,
          layout: true,
          effects: true,
          export: true
        };

        const format = payload.format || 'text';

        // Generate descriptions asynchronously
        const descriptions = await Promise.all(
          selection.map(function(node: SceneNode) {
            return generateDescriptionForNode(node, filters, format);
          })
        );

        let result: string;
        if (format === 'json') {
          if (selection.length === 1) {
            result = descriptions[0];
          } else {
            result = JSON.stringify({
              elements: descriptions.map(function(desc: string) {
                return JSON.parse(desc);
              })
            }, null, 2);
          }
        } else {
          result = descriptions.join('\n\n' + '='.repeat(50) + '\n\n');
        }

        figma.ui.postMessage({
          type: 'DESCRIPTION_GENERATED',
          payload: result
        });
      } catch (error) {
        console.error("Error generating description:", error);
        let errorMessage = "An unknown error occurred.";
        if (error instanceof Error) {
          errorMessage = error.message;
        }
        figma.ui.postMessage({
          type: 'DESCRIPTION_GENERATED',
          payload: 'Error: ' + errorMessage
        });
      }
    }
    else if (type === 'COPY_TO_CLIPBOARD') {
      figma.notify('Copied to clipboard', { timeout: 2000 });
    }
  };
}

// Run the main function
main();

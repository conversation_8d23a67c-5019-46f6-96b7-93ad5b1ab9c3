# Figma Design To Text Plugin

A comprehensive Figma plugin that converts design elements into detailed textual descriptions, generates platform-specific code, and analyzes design systems. Perfect for design documentation, developer handoff, and design system management.

## 🚀 Features

### **Design Analysis**
- **9 Property Categories**: Basic, position, text, appearance, layout, effects, export, advanced, components
- **Vector Analysis**: Complete vector network and path information
- **Boolean Operations**: Complex shape analysis and breakdown
- **Mask Relationships**: Layer masking detection and analysis

### **Code Generation**
- **CSS**: Clean, production-ready CSS using Figma's native API
- **iOS (Swift)**: UIKit code with proper color and layout setup
- **Android (Kotlin)**: View setup with Material Design patterns
- **React (JSX)**: Component code with inline styles

### **Design System Analysis**
- **Color Palette**: Automatic color extraction with usage statistics
- **Typography Scale**: Font analysis and type system detection
- **Spacing System**: Auto-layout and padding pattern analysis
- **Component Readiness**: Development-ready assessment scoring
- **Consistency Scoring**: Design system health metrics

### **Enhanced Style Support**
- **Style References**: Actual style names instead of cryptic IDs
- **Gradient Transforms**: Complete gradient positioning data
- **Design Tokens**: Automatic token detection and analysis

## 📦 Installation

### **From Figma Community** (Recommended)
1. Search for "Design To Text" in Figma Community
2. Click "Install" to add to your plugins

### **Development Installation**
1. Download or clone this repository
2. Open Figma Desktop App
3. Go to **Plugins** → **Development** → **Import plugin from manifest**
4. Select the `manifest.json` file from the project folder

## 🎯 Usage Guide

### **Basic Property Analysis**
1. Select one or more design elements
2. Choose property categories to analyze
3. Select output format (Text or JSON)
4. Click **"Generate Description"**

### **Code Generation**
1. Select a single design element
2. Choose target platform (CSS/iOS/Android/React)
3. Click **"Generate Code"**
4. Copy the generated code to your project

### **Design System Analysis**
1. Select multiple elements representing your design system
2. Click **"Analyze Design System"**
3. Review color palette, typography, spacing, and consistency scores
4. Use insights to improve design system consistency

## 🛠 Development

### **Prerequisites**
- [Node.js](https://nodejs.org) – v22
- [Figma desktop app](https://figma.com/downloads/)

### **Build the plugin**
```bash
npm run build
```

### **Install for development**
1. In the Figma desktop app, open a Figma document
2. Search for and run `Import plugin from manifest…` via Quick Actions
3. Select the `manifest.json` file that was generated by the build script

## 📄 License

MIT License

### Debugging

Use `console.log` statements to inspect values in your code.

To open the developer console, search for and run `Show/Hide Console` via the Quick Actions search bar.

## See also

- [Create Figma Plugin docs](https://yuanqing.github.io/create-figma-plugin/)
- [`yuanqing/figma-plugins`](https://github.com/yuanqing/figma-plugins#readme)

Official docs and code samples from Figma:

- [Plugin API docs](https://figma.com/plugin-docs/)
- [`figma/plugin-samples`](https://github.com/figma/plugin-samples#readme)
